(global["webpackJsonp"] = global["webpackJsonp"] || []).push([["pagesHuYunIm/pages/chat/index"],{

/***/ 80:
/*!*******************************************************************************************************!*\
  !*** D:/Workspace/public/2025/IM-消息系统/im-best/main.js?{"page":"pagesHuYunIm%2Fpages%2Fchat%2Findex"} ***!
  \*******************************************************************************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

"use strict";
/* WEBPACK VAR INJECTION */(function(wx, createPage) {

var _interopRequireDefault = __webpack_require__(/*! @babel/runtime/helpers/interopRequireDefault */ 4);
__webpack_require__(/*! uni-pages */ 26);
var _vue = _interopRequireDefault(__webpack_require__(/*! vue */ 25));
var _index = _interopRequireDefault(__webpack_require__(/*! ./pagesHuYunIm/pages/chat/index.vue */ 81));
// @ts-ignore
wx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;
createPage(_index.default);
/* WEBPACK VAR INJECTION */}.call(this, __webpack_require__(/*! ./node_modules/@dcloudio/uni-mp-weixin/dist/wx.js */ 1)["default"], __webpack_require__(/*! ./node_modules/@dcloudio/uni-mp-weixin/dist/index.js */ 2)["createPage"]))

/***/ }),

/***/ 81:
/*!**********************************************************************************!*\
  !*** D:/Workspace/public/2025/IM-消息系统/im-best/pagesHuYunIm/pages/chat/index.vue ***!
  \**********************************************************************************/
/*! no static exports found */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _index_vue_vue_type_template_id_00dcdc92_scoped_true___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./index.vue?vue&type=template&id=00dcdc92&scoped=true& */ 82);
/* harmony import */ var _index_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./index.vue?vue&type=script&lang=js& */ 84);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _index_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _index_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__[key]; }) }(__WEBPACK_IMPORT_KEY__));
/* harmony import */ var _index_vue_vue_type_style_index_0_id_00dcdc92_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./index.vue?vue&type=style&index=0&id=00dcdc92&lang=scss&scoped=true& */ 87);
/* harmony import */ var _Development_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_runtime_componentNormalizer_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js */ 43);

var renderjs





/* normalize component */

var component = Object(_Development_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_runtime_componentNormalizer_js__WEBPACK_IMPORTED_MODULE_3__["default"])(
  _index_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__["default"],
  _index_vue_vue_type_template_id_00dcdc92_scoped_true___WEBPACK_IMPORTED_MODULE_0__["render"],
  _index_vue_vue_type_template_id_00dcdc92_scoped_true___WEBPACK_IMPORTED_MODULE_0__["staticRenderFns"],
  false,
  null,
  "00dcdc92",
  null,
  false,
  _index_vue_vue_type_template_id_00dcdc92_scoped_true___WEBPACK_IMPORTED_MODULE_0__["components"],
  renderjs
)

component.options.__file = "pagesHuYunIm/pages/chat/index.vue"
/* harmony default export */ __webpack_exports__["default"] = (component.exports);

/***/ }),

/***/ 82:
/*!*****************************************************************************************************************************!*\
  !*** D:/Workspace/public/2025/IM-消息系统/im-best/pagesHuYunIm/pages/chat/index.vue?vue&type=template&id=00dcdc92&scoped=true& ***!
  \*****************************************************************************************************************************/
/*! exports provided: render, staticRenderFns, recyclableRender, components */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _Development_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_Development_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_Development_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_Development_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_Development_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_Development_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_index_vue_vue_type_template_id_00dcdc92_scoped_true___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--17-0!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=template&id=00dcdc92&scoped=true& */ 83);
/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "render", function() { return _Development_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_Development_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_Development_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_Development_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_Development_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_Development_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_index_vue_vue_type_template_id_00dcdc92_scoped_true___WEBPACK_IMPORTED_MODULE_0__["render"]; });

/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "staticRenderFns", function() { return _Development_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_Development_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_Development_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_Development_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_Development_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_Development_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_index_vue_vue_type_template_id_00dcdc92_scoped_true___WEBPACK_IMPORTED_MODULE_0__["staticRenderFns"]; });

/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "recyclableRender", function() { return _Development_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_Development_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_Development_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_Development_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_Development_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_Development_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_index_vue_vue_type_template_id_00dcdc92_scoped_true___WEBPACK_IMPORTED_MODULE_0__["recyclableRender"]; });

/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "components", function() { return _Development_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_Development_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_Development_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_Development_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_Development_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_Development_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_index_vue_vue_type_template_id_00dcdc92_scoped_true___WEBPACK_IMPORTED_MODULE_0__["components"]; });



/***/ }),

/***/ 83:
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--17-0!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!D:/Workspace/public/2025/IM-消息系统/im-best/pagesHuYunIm/pages/chat/index.vue?vue&type=template&id=00dcdc92&scoped=true& ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/*! exports provided: render, staticRenderFns, recyclableRender, components */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "render", function() { return render; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "staticRenderFns", function() { return staticRenderFns; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "recyclableRender", function() { return recyclableRender; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "components", function() { return components; });
var components
try {
  components = {
    mScreenAnimationLihua: function () {
      return __webpack_require__.e(/*! import() | components/m-screen-animation-lihua/m-screen-animation-lihua */ "components/m-screen-animation-lihua/m-screen-animation-lihua").then(__webpack_require__.bind(null, /*! @/components/m-screen-animation-lihua/m-screen-animation-lihua.vue */ 231))
    },
    mScreenAnimationHongbao: function () {
      return __webpack_require__.e(/*! import() | components/m-screen-animation-hongbao/m-screen-animation-hongbao */ "components/m-screen-animation-hongbao/m-screen-animation-hongbao").then(__webpack_require__.bind(null, /*! @/components/m-screen-animation-hongbao/m-screen-animation-hongbao.vue */ 238))
    },
    mGroupSelection: function () {
      return __webpack_require__.e(/*! import() | components/m-group-selection/m-group-selection */ "components/m-group-selection/m-group-selection").then(__webpack_require__.bind(null, /*! @/components/m-group-selection/m-group-selection.vue */ 245))
    },
  }
} catch (e) {
  if (
    e.message.indexOf("Cannot find module") !== -1 &&
    e.message.indexOf(".vue") !== -1
  ) {
    console.error(e.message)
    console.error("1. 排查组件名称拼写是否正确")
    console.error(
      "2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom"
    )
    console.error(
      "3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件"
    )
  } else {
    throw e
  }
}
var render = function () {
  var _vm = this
  var _h = _vm.$createElement
  var _c = _vm._self._c || _h
  var l0 = _vm.__map(_vm.history.messages, function (item, index) {
    var $orig = _vm.__get_orig(item)
    var m0 = !item.isHide ? _vm.renderMessageDate(item, index) : null
    var m1 = !item.isHide && !item.recalled ? _vm.isSelf(item.senderId) : null
    var m2 = !item.isHide && !!item.recalled ? _vm.isSelf(item.senderId) : null
    var m3 =
      !item.isHide && !!item.recalled
        ? item.type === "text" && _vm.isSelf(item.senderId)
        : null
    return {
      $orig: $orig,
      m0: m0,
      m1: m1,
      m2: m2,
      m3: m3,
    }
  })
  _vm.$mp.data = Object.assign(
    {},
    {
      $root: {
        l0: l0,
      },
    }
  )
}
var recyclableRender = false
var staticRenderFns = []
render._withStripped = true



/***/ }),

/***/ 84:
/*!***********************************************************************************************************!*\
  !*** D:/Workspace/public/2025/IM-消息系统/im-best/pagesHuYunIm/pages/chat/index.vue?vue&type=script&lang=js& ***!
  \***********************************************************************************************************/
/*! no static exports found */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _Development_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_Development_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_Development_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_Development_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_Development_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_index_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--13-1!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js& */ 85);
/* harmony import */ var _Development_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_Development_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_Development_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_Development_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_Development_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_index_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_Development_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_Development_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_Development_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_Development_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_Development_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_index_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _Development_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_Development_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_Development_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_Development_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_Development_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_index_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _Development_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_Development_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_Development_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_Development_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_Development_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_index_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__[key]; }) }(__WEBPACK_IMPORT_KEY__));
 /* harmony default export */ __webpack_exports__["default"] = (_Development_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_Development_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_Development_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_Development_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_Development_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_index_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0___default.a); 

/***/ }),

/***/ 85:
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/babel-loader/lib!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--13-1!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!D:/Workspace/public/2025/IM-消息系统/im-best/pagesHuYunIm/pages/chat/index.vue?vue&type=script&lang=js& ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

"use strict";
/* WEBPACK VAR INJECTION */(function(uni) {

var _interopRequireDefault = __webpack_require__(/*! @babel/runtime/helpers/interopRequireDefault */ 4);
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _regenerator = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/regenerator */ 59));
var _defineProperty2 = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/helpers/defineProperty */ 11));
var _toConsumableArray2 = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/helpers/toConsumableArray */ 18));
var _asyncToGenerator2 = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/helpers/asyncToGenerator */ 61));
var _index2 = __webpack_require__(/*! @/TEST/index.js */ 86);
var _vuex = __webpack_require__(/*! vuex */ 38);
var _index3 = __webpack_require__(/*! @/utils/index.js */ 30);
function ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); enumerableOnly && (symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; })), keys.push.apply(keys, symbols); } return keys; }
function _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = null != arguments[i] ? arguments[i] : {}; i % 2 ? ownKeys(Object(source), !0).forEach(function (key) { (0, _defineProperty2.default)(target, key, source[key]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)) : ownKeys(Object(source)).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } return target; }
var navigation = function navigation() {
  Promise.all(/*! require.ensure | pagesHuYunIm/pages/chat/components/navigation/index */[__webpack_require__.e("common/vendor"), __webpack_require__.e("pagesHuYunIm/pages/chat/components/navigation/index")]).then((function () {
    return resolve(__webpack_require__(/*! ./components/navigation/index.vue */ 254));
  }).bind(null, __webpack_require__)).catch(__webpack_require__.oe);
};
var bottomOperation = function bottomOperation() {
  __webpack_require__.e(/*! require.ensure | pagesHuYunIm/pages/chat/components/bottom-operation/index */ "pagesHuYunIm/pages/chat/components/bottom-operation/index").then((function () {
    return resolve(__webpack_require__(/*! ./components/bottom-operation/index.vue */ 262));
  }).bind(null, __webpack_require__)).catch(__webpack_require__.oe);
};
var item = function item() {
  __webpack_require__.e(/*! require.ensure | pagesHuYunIm/pages/chat/components/item/index */ "pagesHuYunIm/pages/chat/components/item/index").then((function () {
    return resolve(__webpack_require__(/*! ./components/item/index */ 269));
  }).bind(null, __webpack_require__)).catch(__webpack_require__.oe);
};
var videoPlayerRef = function videoPlayerRef() {
  __webpack_require__.e(/*! require.ensure | pagesHuYunIm/pages/chat/components/video-player/index */ "pagesHuYunIm/pages/chat/components/video-player/index").then((function () {
    return resolve(__webpack_require__(/*! ./components/video-player/index */ 276));
  }).bind(null, __webpack_require__)).catch(__webpack_require__.oe);
};
var openRedPacket = function openRedPacket() {
  __webpack_require__.e(/*! require.ensure | pagesHuYunIm/pages/chat/components/open-red-packet/index */ "pagesHuYunIm/pages/chat/components/open-red-packet/index").then((function () {
    return resolve(__webpack_require__(/*! ./components/open-red-packet/index */ 283));
  }).bind(null, __webpack_require__)).catch(__webpack_require__.oe);
};
var operate = function operate() {
  __webpack_require__.e(/*! require.ensure | pagesHuYunIm/pages/chat/components/operate/index */ "pagesHuYunIm/pages/chat/components/operate/index").then((function () {
    return resolve(__webpack_require__(/*! ./components/operate/index */ 290));
  }).bind(null, __webpack_require__)).catch(__webpack_require__.oe);
};
var lastMessageTimeStamp = null;
var userInforMap = {};
var envelopeClickList = [];
var innerAudioContext = uni.createInnerAudioContext();
var audioItem = {};
var group = {};
var groupId = null;

// 浏览照片数组
var imageList = [];

// 是否是手动触发的列表滑动
var isBottomOperationScrollToBottom = false;
var IMAGE_MAX_WIDTH = 200;
var IMAGE_MAX_HEIGHT = 150;
var scroll_top = 0;
var reserveHeightRef = 0;
var bottomOperationRefHeight = 0;
var _default = {
  components: {
    // groupSelection,
    navigation: navigation,
    bottomOperation: bottomOperation,
    item: item,
    videoPlayerRef: videoPlayerRef,
    openRedPacket: openRedPacket,
    operate: operate
  },
  name: 'groupChat',
  data: function data() {
    return {
      isHistoryGet: false,
      reserveHeight: 0,
      keyboardheightchangeValue: 0,
      myid: null,
      scroll_top: scroll_top,
      userList: [],
      //群成员列表
      groupCount: '',
      pagueObj: {
        name: '饭搭子5人组'
      },
      to: {},
      // 历史数据
      history: {
        messages: [],
        allLoaded: false
      },
      videoPlayer: {
        show: false,
        url: '',
        context: null
      }
    };
  },
  onLoad: function onLoad(e) {
    var _this = this;
    return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee() {
      var _自己的信息$member_id, member_id;
      return _regenerator.default.wrap(function _callee$(_context) {
        while (1) {
          switch (_context.prev = _context.next) {
            case 0:
              scroll_top = 0;
              _this.scroll_top = scroll_top;
              imageList = [];
              envelopeClickList = uni.getStorageSync('envelopeClickList') || [];
              lastMessageTimeStamp = e.lastMessageTimeStamp || null;
              _this.isHistoryGet = e.lastMessageTimeStamp;
              groupId = e.groupId;
              // 两次进入同一个群就读取第一次进入缓存的数据/否者清空缓存
              if (userInforMap[groupId] !== groupId) {
                userInforMap = {};
                userInforMap[groupId] = groupId;
              }
              _自己的信息$member_id = _index2.自己的信息.member_id, member_id = _自己的信息$member_id === void 0 ? '' : _自己的信息$member_id;
              _this.myid = member_id;
              _this.loadHistoryMessage();
              _this.$nextTick(function () {
                var view = uni.createSelectorQuery().select('.bottomOperationRef');
                view.boundingClientRect(function (ref) {
                  bottomOperationRefHeight = ref.height;
                }).exec();
              });
            case 12:
            case "end":
              return _context.stop();
          }
        }
      }, _callee);
    }))();
  },
  onPageScroll: function onPageScroll() {
    this.$refs.bottomOperationRef.closeAll();
  },
  onReady: function onReady() {
    this.videoPlayer.context = uni.createVideoContext('videoPlayer', this);
  },
  computed: (0, _vuex.mapState)({
    page_font_size: function page_font_size(state) {
      return state.page_font_size;
    },
    //显示时间
    renderMessageDate: function renderMessageDate() {
      var _this2 = this;
      return function (message, index) {
        var _this2$history$messag;
        if (message.timestamp - ((_this2$history$messag = _this2.history.messages[index + 1]) === null || _this2$history$messag === void 0 ? void 0 : _this2$history$messag.timestamp) > 3 * 60 * 1000) {
          return (0, _index3.formatDate)(message.timestamp, 'timestamp');
        }
        return '';
      };
    },
    // 是否本人isMy
    isSelf: function isSelf() {
      return function (senderId) {
        var _自己的信息$member_id2 = _index2.自己的信息.member_id,
          member_id = _自己的信息$member_id2 === void 0 ? '' : _自己的信息$member_id2;
        return senderId === "".concat(member_id);
      };
    },
    envelope_top_opened: function envelope_top_opened() {
      var _this3 = this;
      return function (id) {
        return _this3.envelopeXollectionList.includes(id);
      };
    }
  }),
  methods: {
    setHeight: function setHeight(e) {
      var res = uni.getSystemInfoSync();
      var windowHeight = res.windowHeight;
      // 20 名字向上偏移
      // 8 内边距补偿
      // 4 名字偏移补偿
      var customBar = this.$store.state.StatusBar.customBar - 8 + 4;
      var reserveHeight = windowHeight - e - customBar - bottomOperationRefHeight;
      this.reserveHeight = reserveHeight;
      reserveHeightRef = reserveHeight;
    },
    getHeight: function getHeight(e) {
      var _this4 = this;
      this.$nextTick(function () {
        var view = uni.createSelectorQuery().select('.messageList_');
        view.boundingClientRect(function (select) {
          if (!select) return;
          if (!(select !== null && select !== void 0 && select.height)) {
            _this4.$nextTick(function () {
              var view2 = uni.createSelectorQuery().select('.messageList_');
              view2.boundingClientRect(function (select) {
                _this4.setHeight(select.height);
                if (e) {
                  setTimeout(function () {
                    _this4.reserveHeight = _this4.reserveHeight - _this4.keyboardheightchangeValue;
                  });
                }
              }).exec();
            });
          } else {
            _this4.setHeight(select.height);
            if (e) {
              setTimeout(function () {
                _this4.reserveHeight = _this4.reserveHeight - _this4.keyboardheightchangeValue;
              });
            }
          }
        }).exec();
      });
    },
    //图片加载完成
    imgLoad: function imgLoad(e) {
      if (this.history.messages.length > 20) return;
      this.getHeight(true);
    },
    keyboardheightchange: function keyboardheightchange(e) {
      var e2 = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : false;
      this.keyboardheightchangeValue = e;
      if (reserveHeightRef) {
        this.reserveHeight = reserveHeightRef - e;
      }
      if (e === 0) {
        if (e2) return;
        this.getHeight();
      }
    },
    // 点击整个页面
    onPage: function onPage() {
      this.$refs.bottomOperationRef.close();
      this.$refs.operateRef.close();
    },
    touchmove: function touchmove() {
      // this.$refs.bottomOperationRef.closeAll();
    },
    onBottom: function onBottom() {
      this.$refs.operateRef.close();
    },
    // 输入框获取焦点
    focus: function focus() {
      if (this.isHistoryGet) {
        this.isHistoryGet = false;
        lastMessageTimeStamp = null;
        this.history.messages = [];
        this.loadHistoryMessage();
      }
    },
    // 获取聊天记录
    loadHistoryMessage: function loadHistoryMessage() {
      var _this5 = this;
      uni.hideLoading();
      var list = JSON.parse(JSON.stringify(_index2.对话数据));
      list = list.reverse();
      // 同步混入数据
      list.forEach(function (im, ix) {
        // 缓存照片地址，
        if (im.type === 'image' || im.type === 'image_transmit') {
          imageList.unshift(im.payload.url);
        }
      });
      // 模拟只有少量数据
      // this.history.messages = [list[0],list[1],list[2]];
      this.history.messages = [].concat((0, _toConsumableArray2.default)(this.history.messages), (0, _toConsumableArray2.default)(list));
      if (this.history.messages.length > 20) return;
      this.$nextTick(function () {
        _this5.getHeight();
      });
    },
    onMessageReceived: function onMessageReceived(message) {
      if (message.groupId === group.id) {
        // push进列表
        this.pushList(message);
        //聊天时，收到消息标记为已读
        this.markGroupMessageAsRead();
      }
    },
    // 转发成功后
    sendMessage: function sendMessage(message) {
      // push进列表
      if (message.groupId === groupId) {
        this.pushList(message);
        // 同步消息到首页
        uni.$emit('onMessageReceived', message);
      }
    },
    // 将信息设置为已读
    markGroupMessageAsRead: function markGroupMessageAsRead() {
      //
    },
    // 组装item
    initMessageItem: function initMessageItem(message, index) {
      message['isHide'] = 0;
      // 初始化语音
      if (message.type === 'audio') {
        message['pause'] = 4;
      }
      // 初始化红包
      if (message.type === 'red_envelope') {
        message['had_draw'] = 0;
        message['isClick'] = 0;
        this.setEnvelopeClickList(message, index);
      }
      if (index === 0 && (message.type === 'text' || message.type === 'text_quote')) {
        this.onSetText(message.payload.text);
      }
    },
    // 处理红包是否被点击
    setEnvelopeClickList: function setEnvelopeClickList(im, index) {
      var _this6 = this;
      if (envelopeClickList.includes(im.messageId)) {
        im['isClick'] = 1;
      } else {
        im['isClick'] = 0;
        if (index === 0) {
          this.$nextTick(function () {
            _this6.$refs.mScreenAnimationHongbao.show();
            uni.vibrateLong();
          });
        }
      }
    },
    // 发送信息后，将信息push到列表
    pushList: function pushList(message) {
      var _this7 = this;
      return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee2() {
        return _regenerator.default.wrap(function _callee2$(_context2) {
          while (1) {
            switch (_context2.prev = _context2.next) {
              case 0:
                _this7.initMessageItem(message);
                // 监听到公告
                if (message.type === 'group_notice') {
                  console.log('监听到公告');
                  _this7.$refs.navigationRef.getData();
                }
                // 监听到修改群名
                if (message.type === 'update_group_name') {
                  console.log('监听到修改群名');
                  _this7.pagueObj.name = message.payload.name;
                }
                _this7.history.messages.unshift(message);
                _this7.scrollToBottom();
                if (_this7.history.messages.length < 20) {
                  _this7.getHeight(true);
                }

                // 是否触发文字动效果
                if (message.type === 'text' || message.type === 'text_quote') {
                  _this7.onSetText(message.payload.text);
                }
                // 是否触发红包雨
                if (message.type === 'red_envelope') {
                  _this7.onSetRedEnvelope();
                }

                // 缓存照片地址，
                if (message.type === 'image' || message.type === 'image_transmit') {
                  imageList.push(message.payload.url);
                }
              case 9:
              case "end":
                return _context2.stop();
            }
          }
        }, _callee2);
      }))();
    },
    // 文本触发效果相关========
    onSetText: function onSetText(text) {
      var _this8 = this;
      // 触发礼花
      (0, _index3.throttle)(function () {
        if (text.includes('[彩带]')) {
          _this8.$refs.mScreenAnimationLihua.show();
          uni.vibrateLong();
        }
      }, 4000);
    },
    // 触发红包雨
    onSetRedEnvelope: function onSetRedEnvelope() {
      var _this9 = this;
      (0, _index3.throttle)(function () {
        _this9.$refs.mScreenAnimationHongbao.show();
        uni.vibrateLong();
      }, 4000);
    },
    bottomOperationScrollToBottom: function bottomOperationScrollToBottom() {
      var _this10 = this;
      isBottomOperationScrollToBottom = true;
      setTimeout(function () {
        isBottomOperationScrollToBottom = false;
      }, 800);
      this.$nextTick(function () {
        _this10.scrollToBottom();
      });
    },
    // 页面滚动到底部
    scrollToBottom: function scrollToBottom(e) {
      var _this11 = this;
      this.scroll_top = scroll_top;
      this.$nextTick(function () {
        _this11.scroll_top = 0;
      });
    },
    // 点击某条信息
    onItem: function onItem(item, index) {
      var _this12 = this;
      console.log(item);
      switch (item.type) {
        case 'video':
          this.playVideo(item);
          break;
        case 'audio':
          this.playAudio(item);
          break;
        case 'audio_quote':
          this.playAudio(item);
          break;
        case 'image':
        case 'image_transmit':
          var _index = imageList.indexOf(item.payload.url);
          if (_index === -1) return (0, _index3.openimg)(imageList.length - 1, imageList);
          (0, _index3.openimg)(_index, imageList);
          break;
        case 'red_envelope':
          // 点击红包
          var fun = function fun(code) {
            _this12.renewItem(code, item);
          };
          uni.$off('open_red_packet');
          uni.$on('open_red_packet', fun);
          item['id'] = group.id;
          this.$refs.openRedPacketRef.open(item);
          break;
        case 'map':
          (0, _index3.getLocation)({
            name: item.payload.title,
            address: item.payload.address,
            latitude: item.payload.latitude,
            longitude: item.payload.longitude
          });
          break;
        case 'article':
          (0, _index3.to)("/pagesOne/HTML/index?id=".concat(item.payload.id));
          break;
        case 'share_SBCF':
          (0, _index3.to)('/pagesSBCF/commodity_list/index', {
            id: item.payload.seller_id
          });
          break;
        case 'share_mall':
          (0, _index3.to)("/pagesShopping/details/index", {
            goods_id: item.payload.goods_id
          });
          break;
        case 'functional_module':
          (0, _index3.to)(item.payload.url);
          break;
        default:
          break;
      }
    },
    // 点击红包后更新那一条
    renewItem: function renewItem(code, item) {
      if (code === '0') {
        // 领取
        item.had_draw = 1;
      } else {
        item.isClick = 1;
      }
      // 不这样写某些情况下更新不了视图，
      for (var i = 0; i < this.history.messages.length; i++) {
        if (this.history.messages[i].messageId == item.messageId) {
          this.$set(this.history.messages, i, _objectSpread({}, item));
          break;
        }
      }
    },
    // 长按相关=======================
    // 长按某一条
    onLongpress: function onLongpress(item, e) {
      this.$refs.operateRef.open(item, e);
    },
    // 引用
    quote: function quote(item) {
      this.$refs.bottomOperationRef.quote(item);
    },
    // 谢谢红包
    thank: function thank(item) {
      this.$refs.bottomOperationRef.thank(item);
    },
    // 转发
    transmit: function transmit(item) {
      this.$refs.groupSelectionRef.open(item);
    },
    // 重新编辑
    recalledEdit: function recalledEdit(item) {
      this.$refs.bottomOperationRef.recalledEdit(item);
    },
    // @某人
    mention: function mention(item) {
      this.$refs.bottomOperationRef.mention(item);
    },
    // 视频相关========================
    // 点击了视频并播放
    playVideo: function playVideo(item) {
      var _this13 = this;
      this.videoPlayer.url = item.payload.video.url;
      this.videoPlayer.show = true;
      this.$nextTick(function () {
        _this13.videoPlayer.context.requestFullScreen({
          direction: 0
        });
        _this13.videoPlayer.context.play();
        _this13.videoPlayer.context.showStatusBar();
      });
    },
    // 退出全屏
    onVideoFullScreenChange: function onVideoFullScreenChange(e) {
      //当退出全屏播放时，隐藏播放器
      if (this.videoPlayer.show && !e.detail.fullScreen) {
        this.videoPlayer.show = false;
        this.videoPlayer.context.stop();
      }
    },
    // =============================================
    // 播放语音相关===========
    playAudio: function playAudio(item) {
      (0, _index3.throttle)(function () {
        var _audioItem;
        // pause:1暂停;2播放完,3播放中,4初始状态
        if (item.messageId === ((_audioItem = audioItem) === null || _audioItem === void 0 ? void 0 : _audioItem.messageId)) {
          if (audioItem['pause'] == 3) {
            //正在播放
            // 暂停
            innerAudioContext.pause();
            innerAudioContext.offEnded();
            item['pause'] = 1;
            audioItem['pause'] = 1;
          } else if (audioItem['pause'] == 1 || audioItem['pause'] == 2) {
            //暂停或者播放中
            // 播放
            innerAudioContext.play();
          }
          return;
        }
        audioItem['pause'] = '4';
        audioItem = item;
        if (innerAudioContext) {
          try {
            innerAudioContext.pause();
            innerAudioContext.destroy();
            innerAudioContext = null;
          } catch (e) {}
        }
        innerAudioContext = uni.createInnerAudioContext();
        innerAudioContext.src = item.payload.url;
        innerAudioContext.play();
        innerAudioContext.offEnded();
        innerAudioContext.offPlay();
        innerAudioContext.onPlay(function () {
          // console.log('开始播放');
          item['pause'] = 3;
          audioItem['pause'] = 3;
        });
        innerAudioContext.onEnded(function () {
          // console.log('播放结束');
          item['pause'] = 2;
          audioItem['pause'] = 2;
        });
        innerAudioContext.onError(function (res) {
          console.log('播放异常');
        });
      }, 500);
    },
    // ====================
    // 滚动中
    scroll: function scroll(e) {
      scroll_top = e.detail.scrollTop;
      this.$refs.operateRef.close();
      if (isBottomOperationScrollToBottom) return;
      this.$refs.bottomOperationRef.closeAll();
    },
    // 滚动到底部
    scrolltolower: function scrolltolower() {
      if (this.history.allLoaded) return;
      console.log('触底');
      this.loadHistoryMessage();
    },
    // 滚动到顶部
    scrolltoupper: function scrolltoupper() {}
  }
};
exports.default = _default;
/* WEBPACK VAR INJECTION */}.call(this, __webpack_require__(/*! ./node_modules/@dcloudio/uni-mp-weixin/dist/index.js */ 2)["default"]))

/***/ }),

/***/ 87:
/*!********************************************************************************************************************************************!*\
  !*** D:/Workspace/public/2025/IM-消息系统/im-best/pagesHuYunIm/pages/chat/index.vue?vue&type=style&index=0&id=00dcdc92&lang=scss&scoped=true& ***!
  \********************************************************************************************************************************************/
/*! no static exports found */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _Development_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_Development_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_Development_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_Development_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_Development_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_Development_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_Development_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_Development_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_Development_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_index_vue_vue_type_style_index_0_id_00dcdc92_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--8-oneOf-1-2!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src??ref--8-oneOf-1-3!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--8-oneOf-1-5!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&id=00dcdc92&lang=scss&scoped=true& */ 88);
/* harmony import */ var _Development_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_Development_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_Development_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_Development_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_Development_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_Development_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_Development_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_Development_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_Development_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_index_vue_vue_type_style_index_0_id_00dcdc92_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_Development_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_Development_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_Development_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_Development_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_Development_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_Development_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_Development_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_Development_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_Development_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_index_vue_vue_type_style_index_0_id_00dcdc92_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_0__);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _Development_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_Development_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_Development_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_Development_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_Development_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_Development_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_Development_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_Development_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_Development_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_index_vue_vue_type_style_index_0_id_00dcdc92_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_0__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _Development_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_Development_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_Development_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_Development_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_Development_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_Development_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_Development_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_Development_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_Development_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_index_vue_vue_type_style_index_0_id_00dcdc92_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_0__[key]; }) }(__WEBPACK_IMPORT_KEY__));
 /* harmony default export */ __webpack_exports__["default"] = (_Development_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_Development_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_Development_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_Development_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_Development_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_Development_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_Development_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_Development_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_Development_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_index_vue_vue_type_style_index_0_id_00dcdc92_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_0___default.a); 

/***/ }),

/***/ 88:
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!./node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--8-oneOf-1-2!./node_modules/postcss-loader/src??ref--8-oneOf-1-3!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--8-oneOf-1-5!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!D:/Workspace/public/2025/IM-消息系统/im-best/pagesHuYunIm/pages/chat/index.vue?vue&type=style&index=0&id=00dcdc92&lang=scss&scoped=true& ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

// extracted by mini-css-extract-plugin
    if(false) { var cssReload; }
  

/***/ })

},[[80,"common/runtime","common/vendor"]]]);
//# sourceMappingURL=../../../../.sourcemap/mp-weixin/pagesHuYunIm/pages/chat/index.js.map